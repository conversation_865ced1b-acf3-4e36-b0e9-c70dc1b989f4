#!/usr/bin/env python3
"""
启动测试脚本 - 验证系统能否正常启动
"""
import sys
import os
import time
import subprocess
import requests
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class StartupTester:
    """启动测试器"""
    
    def __init__(self):
        self.backend_process = None
        self.frontend_process = None
    
    def cleanup(self):
        """清理进程"""
        print("🧹 清理测试进程...")
        
        # 停止后端进程
        if self.backend_process:
            self.backend_process.terminate()
            try:
                self.backend_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.backend_process.kill()
        
        # 停止前端进程
        if self.frontend_process:
            self.frontend_process.terminate()
            try:
                self.frontend_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.frontend_process.kill()
        
        # 强制清理端口占用
        os.system("lsof -ti:8000 | xargs kill -9 2>/dev/null || true")
        os.system("lsof -ti:3000 | xargs kill -9 2>/dev/null || true")
        
        print("✅ 清理完成")
    
    def test_backend_startup(self):
        """测试后端启动"""
        print("🔧 测试后端启动...")
        
        try:
            # 启动后端
            self.backend_process = subprocess.Popen(
                [sys.executable, "scripts/start_backend.py"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=project_root
            )
            
            # 等待启动
            print("   ⏳ 等待后端启动...")
            time.sleep(5)
            
            # 检查进程状态
            if self.backend_process.poll() is not None:
                stdout, stderr = self.backend_process.communicate()
                print(f"   ❌ 后端进程异常退出")
                print(f"   stdout: {stdout.decode()}")
                print(f"   stderr: {stderr.decode()}")
                return False
            
            # 测试健康检查接口
            try:
                response = requests.get("http://localhost:8000/health", timeout=5)
                if response.status_code == 200:
                    print("   ✅ 后端启动成功")
                    print(f"   健康状态: {response.json()}")
                    return True
                else:
                    print(f"   ❌ 健康检查失败: {response.status_code}")
                    return False
            except requests.RequestException as e:
                print(f"   ❌ 无法连接到后端: {e}")
                return False
                
        except Exception as e:
            print(f"   ❌ 后端启动异常: {e}")
            return False
    
    def test_api_endpoints(self):
        """测试API端点"""
        print("🌐 测试API端点...")
        
        endpoints = [
            ("GET", "/", "根路径"),
            ("GET", "/strategies", "策略列表"),
            ("GET", "/system/status", "系统状态")
        ]
        
        success_count = 0
        
        for method, endpoint, description in endpoints:
            try:
                if method == "GET":
                    response = requests.get(f"http://localhost:8000{endpoint}", timeout=5)
                
                if response.status_code == 200:
                    print(f"   ✅ {description}: {response.status_code}")
                    success_count += 1
                else:
                    print(f"   ⚠️  {description}: {response.status_code}")
                    
            except requests.RequestException as e:
                print(f"   ❌ {description}: {e}")
        
        print(f"   API测试结果: {success_count}/{len(endpoints)} 成功")
        return success_count == len(endpoints)
    
    def test_frontend_build(self):
        """测试前端构建"""
        print("⚛️  测试前端构建...")
        
        try:
            # 检查前端目录
            frontend_dir = project_root / "frontend"
            if not frontend_dir.exists():
                print("   ❌ frontend目录不存在")
                return False
            
            # 检查package.json
            package_json = frontend_dir / "package.json"
            if not package_json.exists():
                print("   ❌ package.json不存在")
                return False
            
            # 检查node_modules
            node_modules = frontend_dir / "node_modules"
            if not node_modules.exists():
                print("   ⚠️  node_modules不存在，需要运行npm install")
                return False
            
            print("   ✅ 前端环境检查通过")
            return True
            
        except Exception as e:
            print(f"   ❌ 前端检查异常: {e}")
            return False
    
    def test_data_connectivity(self):
        """测试数据连接"""
        print("📊 测试数据连接...")
        
        try:
            from src.data.data_manager import DataManager
            
            dm = DataManager()
            
            # 测试数据获取
            print("   🔍 测试数据获取...")
            data = dm.get_stock_data('AAPL', '2024-01-01', '2024-01-05')
            
            if not data.empty:
                print(f"   ✅ 数据获取成功: {len(data)}条记录")
                return True
            else:
                print("   ⚠️  数据获取返回空结果")
                return False
                
        except Exception as e:
            print(f"   ❌ 数据连接异常: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有启动测试"""
        print("🚀 开始启动测试...")
        print("="*50)
        
        results = {}
        
        try:
            # 测试后端启动
            results['backend'] = self.test_backend_startup()
            
            if results['backend']:
                # 测试API端点
                results['api'] = self.test_api_endpoints()
            else:
                results['api'] = False
                print("🌐 跳过API测试（后端未启动）")
            
            # 测试前端环境
            results['frontend'] = self.test_frontend_build()
            
            # 测试数据连接
            results['data'] = self.test_data_connectivity()
            
        except KeyboardInterrupt:
            print("\n⚠️  测试被用户中断")
        except Exception as e:
            print(f"❌ 测试过程异常: {e}")
        finally:
            self.cleanup()
        
        # 生成报告
        print("\n" + "="*50)
        print("📋 启动测试报告")
        print("="*50)
        
        total_tests = len(results)
        passed_tests = sum(results.values())
        
        for test_name, result in results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"   {test_name.ljust(10)}: {status}")
        
        print(f"\n总体结果: {passed_tests}/{total_tests} 测试通过")
        
        if passed_tests == total_tests:
            print("🎉 所有启动测试通过！系统可以正常启动")
            return True
        else:
            print("⚠️  部分测试失败，请检查相关问题")
            return False


def main():
    """主函数"""
    tester = StartupTester()
    
    try:
        success = tester.run_all_tests()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ 测试脚本异常: {e}")
        tester.cleanup()
        sys.exit(1)


if __name__ == "__main__":
    main()
