"""
API集成测试
"""
import pytest
import requests
import time


class TestAPIIntegration:
    """API集成测试"""
    
    @pytest.fixture(autouse=True)
    def setup(self, test_config):
        """测试设置"""
        self.base_url = test_config['api_base_url']
        self.test_symbol = test_config['test_symbol']
    
    def test_health_check(self, api_client):
        """测试健康检查端点"""
        try:
            response = api_client.get(f"{self.base_url}/health", timeout=5)
            assert response.status_code == 200
            data = response.json()
            assert 'status' in data
        except requests.exceptions.ConnectionError:
            pytest.skip("后端服务未启动")
    
    def test_strategies_endpoint(self, api_client):
        """测试策略列表端点"""
        try:
            response = api_client.get(f"{self.base_url}/strategies", timeout=5)
            assert response.status_code == 200
            
            strategies = response.json()
            assert isinstance(strategies, list)
            assert len(strategies) > 0
            
            # 检查策略结构
            for strategy in strategies:
                assert 'name' in strategy
                assert 'description' in strategy
                assert 'parameters' in strategy
        except requests.exceptions.ConnectionError:
            pytest.skip("后端服务未启动")
    
    def test_market_data_endpoint(self, api_client):
        """测试市场数据端点"""
        try:
            payload = {
                "symbol": self.test_symbol,
                "start_date": "2023-01-01",
                "end_date": "2023-01-31"
            }
            
            response = api_client.post(
                f"{self.base_url}/market-data",
                json=payload,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                assert data['success'] == True
                assert 'data' in data
                assert len(data['data']['data']) > 0
        except requests.exceptions.ConnectionError:
            pytest.skip("后端服务未启动")
    
    def test_backtest_endpoint(self, api_client):
        """测试回测端点"""
        try:
            payload = {
                "symbol": self.test_symbol,
                "strategy": "moving_average",
                "parameters": {
                    "fast_period": 10,
                    "slow_period": 20
                },
                "start_date": "2023-01-01",
                "end_date": "2023-03-31",
                "initial_capital": 10000
            }
            
            response = api_client.post(
                f"{self.base_url}/backtest",
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                assert data['success'] == True
                assert 'data' in data
                
                # 检查回测结果结构
                result_data = data['data']
                required_fields = [
                    'total_return', 'annualized_return', 'sharpe_ratio',
                    'max_drawdown', 'num_trades'
                ]
                for field in required_fields:
                    assert field in result_data
        except requests.exceptions.ConnectionError:
            pytest.skip("后端服务未启动")
    
    def test_system_status_endpoint(self, api_client):
        """测试系统状态端点"""
        try:
            response = api_client.get(f"{self.base_url}/system/status", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                assert 'status' in data
                assert 'timestamp' in data
        except requests.exceptions.ConnectionError:
            pytest.skip("后端服务未启动")
    
    def test_api_error_handling(self, api_client):
        """测试API错误处理"""
        try:
            # 测试无效的策略
            payload = {
                "symbol": self.test_symbol,
                "strategy": "invalid_strategy",
                "parameters": {},
                "start_date": "2023-01-01",
                "end_date": "2023-01-31",
                "initial_capital": 10000
            }
            
            response = api_client.post(
                f"{self.base_url}/backtest",
                json=payload,
                timeout=10
            )
            
            assert response.status_code == 400
        except requests.exceptions.ConnectionError:
            pytest.skip("后端服务未启动")
