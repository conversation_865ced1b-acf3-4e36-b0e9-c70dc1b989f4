import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  ComposedChart,
  Bar,
  ReferenceLine
} from 'recharts';
import { Card, Row, Col } from 'antd';

const PerformanceChart = ({ data }) => {
  if (!data || data.length === 0) {
    return <div>暂无图表数据</div>;
  }

  // 处理数据格式
  const chartData = data.map(item => ({
    date: new Date(item.date).toLocaleDateString(),
    portfolio_value: item.total,
    price: item.price,
    signal: item.signal,
    returns: item.returns * 100 // 转换为百分比
  }));

  // 计算回撤数据
  const portfolioValues = chartData.map(item => item.portfolio_value);
  const runningMax = [];
  let currentMax = portfolioValues[0];
  
  const drawdownData = portfolioValues.map((value, index) => {
    if (value > currentMax) {
      currentMax = value;
    }
    runningMax.push(currentMax);
    
    const drawdown = ((value - currentMax) / currentMax) * 100;
    return {
      date: chartData[index].date,
      drawdown: drawdown
    };
  });

  // 自定义Tooltip
  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div style={{
          backgroundColor: 'white',
          padding: '10px',
          border: '1px solid #ccc',
          borderRadius: '4px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
        }}>
          <p style={{ margin: 0, fontWeight: 'bold' }}>{`日期: ${label}`}</p>
          {payload.map((entry, index) => (
            <p key={index} style={{ margin: '4px 0', color: entry.color }}>
              {`${entry.name}: ${entry.value.toFixed(2)}${entry.name.includes('回撤') || entry.name.includes('收益') ? '%' : ''}`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  // 信号点数据
  const signalData = chartData.filter(item => item.signal !== 0);

  return (
    <div>
      <Row gutter={16}>
        <Col span={24}>
          <Card title="组合价值走势" style={{ marginBottom: 16 }}>
            <ResponsiveContainer width="100%" height={400}>
              <ComposedChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="date" 
                  tick={{ fontSize: 12 }}
                  interval="preserveStartEnd"
                />
                <YAxis yAxisId="left" />
                <YAxis yAxisId="right" orientation="right" />
                <Tooltip content={<CustomTooltip />} />
                <Legend />
                
                <Line
                  yAxisId="left"
                  type="monotone"
                  dataKey="portfolio_value"
                  stroke="#1890ff"
                  strokeWidth={2}
                  dot={false}
                  name="组合价值"
                />
                
                <Line
                  yAxisId="right"
                  type="monotone"
                  dataKey="price"
                  stroke="#52c41a"
                  strokeWidth={1}
                  dot={false}
                  name="股票价格"
                />
                
                {/* 买入信号 */}
                {signalData.filter(item => item.signal === 1).map((item, index) => (
                  <ReferenceLine
                    key={`buy-${index}`}
                    x={item.date}
                    stroke="#52c41a"
                    strokeDasharray="2 2"
                  />
                ))}
                
                {/* 卖出信号 */}
                {signalData.filter(item => item.signal === -1).map((item, index) => (
                  <ReferenceLine
                    key={`sell-${index}`}
                    x={item.date}
                    stroke="#ff4d4f"
                    strokeDasharray="2 2"
                  />
                ))}
              </ComposedChart>
            </ResponsiveContainer>
          </Card>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <Card title="回撤分析">
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={drawdownData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="date" 
                  tick={{ fontSize: 12 }}
                  interval="preserveStartEnd"
                />
                <YAxis />
                <Tooltip content={<CustomTooltip />} />
                <Legend />
                
                <Line
                  type="monotone"
                  dataKey="drawdown"
                  stroke="#ff4d4f"
                  strokeWidth={2}
                  dot={false}
                  name="回撤 (%)"
                  fill="#ff4d4f"
                />
                
                <ReferenceLine y={0} stroke="#666" strokeDasharray="1 1" />
              </LineChart>
            </ResponsiveContainer>
          </Card>
        </Col>

        <Col span={12}>
          <Card title="日收益率分布">
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="date" 
                  tick={{ fontSize: 12 }}
                  interval="preserveStartEnd"
                />
                <YAxis />
                <Tooltip content={<CustomTooltip />} />
                <Legend />
                
                <Bar
                  dataKey="returns"
                  fill="#1890ff"
                  name="日收益率 (%)"
                />
                
                <ReferenceLine y={0} stroke="#666" strokeDasharray="1 1" />
              </LineChart>
            </ResponsiveContainer>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default PerformanceChart;
