import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Table,
  Tabs,
  Button,
  message,
  Space,
  Tag
} from 'antd';
import {
  CopyOutlined,
  TrophyOutlined,
  DollarOutlined,
  LineChartOutlined,
  BarChartOutlined
} from '@ant-design/icons';
import PerformanceChart from './PerformanceChart';

const { TabPane } = Tabs;

const ResultsDisplay = ({ results }) => {
  const [activeTab, setActiveTab] = useState('overview');

  const copyResults = () => {
    const text = `
回测结果摘要
====================
总收益率: ${formatPercentage(results.total_return)}
年化收益率: ${formatPercentage(results.annualized_return)}
夏普比率: ${results.sharpe_ratio?.toFixed(2) || 'N/A'}
最大回撤: ${formatPercentage(Math.abs(results.max_drawdown))}
交易次数: ${results.num_trades || 0}
胜率: ${formatPercentage(results.win_rate)}
盈亏比: ${results.profit_factor?.toFixed(2) || 'N/A'}
最佳交易: ${results.best_trade?.toFixed(2) || 'N/A'}
最差交易: ${results.worst_trade?.toFixed(2) || 'N/A'}
净利润: ${results.net_profit?.toFixed(2) || 'N/A'}
最大连续盈利: ${results.max_consecutive_wins || 0}
最大连续亏损: ${results.max_consecutive_losses || 0}
====================
生成时间: ${new Date().toLocaleString()}
    `;

    navigator.clipboard.writeText(text).then(() => {
      message.success('结果已复制到剪贴板');
    }).catch(() => {
      message.error('复制失败');
    });
  };

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(value);
  };

  const formatPercentage = (value) => {
    return `${(value * 100).toFixed(2)}%`;
  };

  const getValueColor = (value) => {
    if (value > 0) return '#52c41a';
    if (value < 0) return '#ff4d4f';
    return '#666';
  };

  const tradesColumns = [
    {
      title: '日期',
      dataIndex: 'date',
      key: 'date',
      render: (date) => new Date(date).toLocaleDateString()
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type) => (
        <Tag color={type === 'BUY' ? 'green' : 'red'}>
          {type === 'BUY' ? '买入' : '卖出'}
        </Tag>
      )
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
      render: (price) => formatCurrency(price)
    },
    {
      title: '数量',
      dataIndex: 'shares',
      key: 'shares'
    },
    {
      title: '金额',
      dataIndex: 'cost',
      key: 'cost',
      render: (cost, record) => {
        const amount = record.type === 'BUY' ? cost : record.revenue;
        return formatCurrency(amount);
      }
    },
    {
      title: '盈亏',
      dataIndex: 'pnl',
      key: 'pnl',
      render: (pnl) => (
        <span style={{ color: getValueColor(pnl) }}>
          {pnl ? formatCurrency(pnl) : '-'}
        </span>
      )
    }
  ];

  return (
    <div className="results-container">
      <Card
        title={
          <Space>
            <TrophyOutlined />
            回测结果
          </Space>
        }
        extra={
          <Button
            icon={<CopyOutlined />}
            onClick={copyResults}
          >
            复制结果
          </Button>
        }
      >
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="概览" key="overview">
            <Row gutter={16}>
              <Col span={6}>
                <Card className="metric-card">
                  <Statistic
                    title="总收益率"
                    value={formatPercentage(results.total_return)}
                    valueStyle={{ color: getValueColor(results.total_return) }}
                    prefix={<DollarOutlined />}
                  />
                </Card>
              </Col>
              
              <Col span={6}>
                <Card className="metric-card">
                  <Statistic
                    title="年化收益率"
                    value={results.annualized_return * 100}
                    precision={2}
                    suffix="%"
                    valueStyle={{ color: getValueColor(results.annualized_return) }}
                    prefix={<LineChartOutlined />}
                  />
                </Card>
              </Col>
              
              <Col span={6}>
                <Card className="metric-card">
                  <Statistic
                    title="夏普比率"
                    value={results.sharpe_ratio}
                    precision={2}
                    valueStyle={{ color: getValueColor(results.sharpe_ratio) }}
                    prefix={<BarChartOutlined />}
                  />
                </Card>
              </Col>
              
              <Col span={6}>
                <Card className="metric-card">
                  <Statistic
                    title="最大回撤"
                    value={Math.abs(results.max_drawdown) * 100}
                    precision={2}
                    suffix="%"
                    valueStyle={{ color: '#ff4d4f' }}
                  />
                </Card>
              </Col>
            </Row>

            <Row gutter={16} style={{ marginTop: 16 }}>
              <Col span={6}>
                <Card className="metric-card">
                  <Statistic
                    title="交易次数"
                    value={results.num_trades || 0}
                    valueStyle={{ color: '#1890ff' }}
                  />
                </Card>
              </Col>
              
              <Col span={6}>
                <Card className="metric-card">
                  <Statistic
                    title="胜率"
                    value={results.win_rate * 100}
                    precision={2}
                    suffix="%"
                    valueStyle={{ color: getValueColor(results.win_rate - 0.5) }}
                  />
                </Card>
              </Col>
              
              <Col span={6}>
                <Card className="metric-card">
                  <Statistic
                    title="盈亏比"
                    value={results.profit_factor}
                    precision={2}
                    valueStyle={{ color: getValueColor(results.profit_factor - 1) }}
                  />
                </Card>
              </Col>
              
              <Col span={6}>
                <Card className="metric-card">
                  <Statistic
                    title="最终价值"
                    value={results.final_value}
                    precision={2}
                    formatter={(value) => formatCurrency(value)}
                    valueStyle={{ color: getValueColor(results.total_return) }}
                  />
                </Card>
              </Col>
            </Row>
          </TabPane>

          <TabPane tab="图表分析" key="charts">
            <PerformanceChart data={results.portfolio} />
          </TabPane>

          <TabPane tab="交易记录" key="trades">
            <Table
              columns={tradesColumns}
              dataSource={results.trades || []}
              rowKey={(record, index) => index}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`
              }}
            />
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default ResultsDisplay;
