import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Progress,
  Alert,
  Button,
  Space,
  Typography,
  Badge,
  Divider
} from 'antd';
import {
  ReloadOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined
} from '@ant-design/icons';
import { getSystemStatus, getPerformanceMetrics } from '../services/api';

const { Title, Text } = Typography;

const SystemMonitor = () => {
  const [systemStatus, setSystemStatus] = useState(null);
  const [performance, setPerformance] = useState(null);
  const [loading, setLoading] = useState(false);
  const [lastUpdate, setLastUpdate] = useState(null);

  const fetchData = async () => {
    setLoading(true);
    try {
      const [statusData, perfData] = await Promise.all([
        getSystemStatus(),
        getPerformanceMetrics()
      ]);
      
      setSystemStatus(statusData);
      setPerformance(perfData.success ? perfData.data : null);
      setLastUpdate(new Date().toLocaleTimeString());
    } catch (error) {
      console.error('Failed to fetch monitoring data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
    const interval = setInterval(fetchData, 30000); // 每30秒更新一次
    return () => clearInterval(interval);
  }, []);

  const getStatusIcon = (status) => {
    switch (status) {
      case 'healthy':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'warning':
        return <ExclamationCircleOutlined style={{ color: '#faad14' }} />;
      case 'error':
        return <CloseCircleOutlined style={{ color: '#f5222d' }} />;
      default:
        return null;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'healthy':
        return 'success';
      case 'warning':
        return 'warning';
      case 'error':
        return 'error';
      default:
        return 'default';
    }
  };

  const renderHealthChecks = () => {
    if (!systemStatus?.health_checks) return null;

    return Object.entries(systemStatus.health_checks).map(([key, check]) => (
      <Card key={key} size="small" style={{ marginBottom: 8 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Space>
            {getStatusIcon(check.status)}
            <Text strong>{key.replace('_', ' ').toUpperCase()}</Text>
          </Space>
          <Badge status={getStatusColor(check.status)} text={check.status} />
        </div>
        {check.error && (
          <Text type="danger" style={{ fontSize: '12px' }}>
            {check.error}
          </Text>
        )}
        {check.issues && check.issues.length > 0 && (
          <div style={{ marginTop: 4 }}>
            {check.issues.map((issue, idx) => (
              <Text key={idx} type="warning" style={{ fontSize: '12px', display: 'block' }}>
                • {issue}
              </Text>
            ))}
          </div>
        )}
      </Card>
    ));
  };

  return (
    <div style={{ padding: '20px' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
        <Title level={3}>系统监控</Title>
        <Space>
          {lastUpdate && <Text type="secondary">最后更新: {lastUpdate}</Text>}
          <Button 
            icon={<ReloadOutlined />} 
            onClick={fetchData} 
            loading={loading}
          >
            刷新
          </Button>
        </Space>
      </div>

      {/* 总体状态 */}
      {systemStatus && (
        <Alert
          message={`系统状态: ${systemStatus.status}`}
          type={getStatusColor(systemStatus.status)}
          icon={getStatusIcon(systemStatus.status)}
          style={{ marginBottom: '20px' }}
        />
      )}

      <Row gutter={[16, 16]}>
        {/* 系统资源 */}
        {performance?.system_info && (
          <Col xs={24} md={12} lg={8}>
            <Card title="系统资源" size="small">
              <Row gutter={[8, 8]}>
                <Col span={24}>
                  <div style={{ marginBottom: 16 }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Text>CPU使用率</Text>
                      <Text>{performance.system_info.cpu_percent.toFixed(1)}%</Text>
                    </div>
                    <Progress 
                      percent={performance.system_info.cpu_percent} 
                      size="small"
                      status={performance.system_info.cpu_percent > 80 ? 'exception' : 'normal'}
                    />
                  </div>
                </Col>
                <Col span={24}>
                  <div style={{ marginBottom: 16 }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Text>内存使用率</Text>
                      <Text>{performance.system_info.memory_percent.toFixed(1)}%</Text>
                    </div>
                    <Progress 
                      percent={performance.system_info.memory_percent} 
                      size="small"
                      status={performance.system_info.memory_percent > 85 ? 'exception' : 'normal'}
                    />
                  </div>
                </Col>
                <Col span={24}>
                  <div style={{ marginBottom: 16 }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Text>磁盘使用率</Text>
                      <Text>{performance.system_info.disk_usage.toFixed(1)}%</Text>
                    </div>
                    <Progress 
                      percent={performance.system_info.disk_usage} 
                      size="small"
                      status={performance.system_info.disk_usage > 90 ? 'exception' : 'normal'}
                    />
                  </div>
                </Col>
                <Col span={12}>
                  <Statistic
                    title="可用内存"
                    value={performance.system_info.memory_available}
                    precision={2}
                    suffix="GB"
                    valueStyle={{ fontSize: '16px' }}
                  />
                </Col>
              </Row>
            </Card>
          </Col>
        )}

        {/* 健康检查 */}
        <Col xs={24} md={12} lg={8}>
          <Card title="健康检查" size="small">
            {renderHealthChecks()}
          </Card>
        </Col>

        {/* 系统信息 */}
        <Col xs={24} md={12} lg={8}>
          <Card title="系统信息" size="small">
            {systemStatus?.components && (
              <>
                <Row gutter={[8, 8]}>
                  <Col span={12}>
                    <Statistic
                      title="可用策略"
                      value={systemStatus.components.strategies}
                      valueStyle={{ fontSize: '16px' }}
                    />
                  </Col>
                  <Col span={12}>
                    <div>
                      <Text type="secondary">版本</Text>
                      <br />
                      <Text strong>{systemStatus.version}</Text>
                    </div>
                  </Col>
                </Row>
                <Divider />
                <div>
                  <Text type="secondary">API状态</Text>
                  <br />
                  <Badge 
                    status={getStatusColor(systemStatus.components.api)} 
                    text={systemStatus.components.api} 
                  />
                </div>
              </>
            )}
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default SystemMonitor;
