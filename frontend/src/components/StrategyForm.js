import React, { useState } from 'react';
import {
  Form,
  Input,
  Select,
  InputNumber,
  DatePicker,
  <PERSON>ton,
  Card,
  Row,
  Col,
  Divider
} from 'antd';
import { PlayCircleOutlined } from '@ant-design/icons';
import moment from 'moment';

const { Option } = Select;
const { RangePicker } = DatePicker;

const StrategyForm = ({ strategies, onSubmit, loading }) => {
  const [form] = Form.useForm();
  const [selectedStrategy, setSelectedStrategy] = useState(null);
  const [strategyParams, setStrategyParams] = useState({});

  const handleStrategyChange = (strategyName) => {
    const strategy = strategies.find(s => s.name === strategyName);
    setSelectedStrategy(strategy);
    setStrategyParams({});
    
    // 重置参数表单
    const paramFields = {};
    if (strategy && strategy.parameters) {
      Object.keys(strategy.parameters).forEach(key => {
        paramFields[key] = strategy.parameters[key].default;
      });
    }
    setStrategyParams(paramFields);
  };

  const handleParamChange = (paramName, value) => {
    setStrategyParams(prev => ({
      ...prev,
      [paramName]: value
    }));
  };

  const handleSubmit = (values) => {
    const formData = {
      symbol: values.symbol,
      strategy: values.strategy,
      start_date: values.dateRange[0].toISOString(),
      end_date: values.dateRange[1].toISOString(),
      initial_capital: values.initial_capital,
      commission: values.commission / 100,
      slippage: values.slippage / 100,
      parameters: strategyParams
    };
    onSubmit(formData);
  };

  const renderParameterInputs = () => {
    if (!selectedStrategy || !selectedStrategy.parameters) return null;

    return Object.entries(selectedStrategy.parameters).map(([key, param]) => (
      <Col span={8} key={key}>
        <Form.Item label={param.description || key}>
          <InputNumber
            value={strategyParams[key] || param.default}
            onChange={(value) => handleParamChange(key, value)}
            min={param.min}
            max={param.max}
            step={param.step || 0.01}
            style={{ width: '100%' }}
          />
        </Form.Item>
      </Col>
    ));
  };

  return (
    <Card
      title="策略回测配置"
      style={{
        margin: '0 0 16px 0',
        fontSize: '14px'
      }}
      size="small"
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        size="small"
        initialValues={{
          symbol: 'AAPL',
          strategy: strategies[0]?.name,
          dateRange: [moment().subtract(1, 'year'), moment()],
          initial_capital: 10000,
          commission: 0.1,
          slippage: 0.1
        }}
      >
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item
              label="股票代码"
              name="symbol"
              rules={[{ required: true, message: '请输入股票代码' }]}
            >
              <Input placeholder="输入股票代码 (如: AAPL)" />
            </Form.Item>
          </Col>
          
          <Col span={8}>
            <Form.Item
              label="交易策略"
              name="strategy"
              rules={[{ required: true, message: '请选择交易策略' }]}
            >
              <Select onChange={handleStrategyChange}>
                {strategies.map(strategy => (
                  <Option key={strategy.name} value={strategy.name}>
                    {strategy.description}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          
          <Col span={8}>
            <Form.Item
              label="回测时间范围"
              name="dateRange"
              rules={[{ required: true, message: '请选择时间范围' }]}
            >
              <RangePicker style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>

        {selectedStrategy && selectedStrategy.parameters && 
         Object.keys(selectedStrategy.parameters).length > 0 && (
          <>
            <Divider>策略参数</Divider>
            <Row gutter={16}>
              {renderParameterInputs()}
            </Row>
          </>
        )}

        <Divider>交易设置</Divider>
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item
              label="初始资金"
              name="initial_capital"
              rules={[{ required: true, message: '请输入初始资金' }]}
            >
              <InputNumber
                min={1000}
                max={10000000}
                step={1000}
                style={{ width: '100%' }}
                formatter={value => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                parser={value => value.replace(/\$\s?|(,*)/g, '')}
              />
            </Form.Item>
          </Col>
          
          <Col span={8}>
            <Form.Item
              label="手续费 (%)"
              name="commission"
              rules={[{ required: true, message: '请输入手续费' }]}
            >
              <InputNumber
                min={0}
                max={5}
                step={0.01}
                style={{ width: '100%' }}
              />
            </Form.Item>
          </Col>
          
          <Col span={8}>
            <Form.Item
              label="滑点 (%)"
              name="slippage"
              rules={[{ required: true, message: '请输入滑点' }]}
            >
              <InputNumber
                min={0}
                max={5}
                step={0.01}
                style={{ width: '100%' }}
              />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
            size="large"
            icon={<PlayCircleOutlined />}
            style={{ width: '200px' }}
          >
            开始回测
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );
};

export default StrategyForm;
