import React, { useState, useEffect } from 'react';
import { Layout, Typography, message, Menu } from 'antd';
import { 
  DashboardOutlined, 
  BarChartOutlined, 
  MonitorOutlined 
} from '@ant-design/icons';
import StrategyForm from './components/StrategyForm';
import ResultsDisplay from './components/ResultsDisplay';
import LoadingSpinner from './components/LoadingSpinner';
import ErrorBoundary from './components/ErrorBoundary';
import SystemMonitor from './components/SystemMonitor';
import { getStrategies, runBacktest } from './services/api';

const { Header, Content, Sider } = Layout;
const { Title } = Typography;

function App() {
  const [strategies, setStrategies] = useState([]);
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState(null);
  const [currentView, setCurrentView] = useState('backtest');

  useEffect(() => {
    loadStrategies();
  }, []);

  const loadStrategies = async () => {
    try {
      const data = await getStrategies();
      setStrategies(data);
    } catch (error) {
      message.error('加载策略失败: ' + error.message);
    }
  };

  const handleBacktest = async (formData) => {
    setLoading(true);
    setResults(null);
    
    try {
      message.loading('正在运行回测...', 0);
      const result = await runBacktest(formData);
      message.destroy();
      
      if (result.success) {
        setResults(result.data);
        message.success({
          content: '回测完成！',
          duration: 3,
        });
      } else {
        message.error({
          content: '回测失败: ' + result.error,
          duration: 5,
        });
      }
    } catch (error) {
      message.destroy();
      console.error('Backtest error:', error);
      message.error({
        content: '回测失败: ' + (error.message || '未知错误'),
        duration: 5,
      });
    } finally {
      setLoading(false);
    }
  };

  const menuItems = [
    {
      key: 'backtest',
      icon: <BarChartOutlined />,
      label: '策略回测',
    },
    {
      key: 'monitor',
      icon: <MonitorOutlined />,
      label: '系统监控',
    }
  ];

  const renderContent = () => {
    switch (currentView) {
      case 'monitor':
        return <SystemMonitor />;
      case 'backtest':
      default:
        return (
          <>
            <StrategyForm
              strategies={strategies}
              onSubmit={handleBacktest}
              loading={loading}
            />
            {loading && (
              <LoadingSpinner 
                message="正在运行回测，请稍候..." 
                size="large" 
              />
            )}
            {results && <ResultsDisplay results={results} />}
          </>
        );
    }
  };

  return (
    <ErrorBoundary>
      <Layout style={{ minHeight: '100vh', maxWidth: '1400px', margin: '0 auto' }}>
        <Header style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '0 16px',
          height: '56px'
        }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <DashboardOutlined style={{ fontSize: '20px', color: 'white', marginRight: '8px' }} />
            <Title level={5} style={{ color: 'white', margin: 0, fontSize: '16px' }}>
              量化交易系统 v3.0
            </Title>
          </div>
        </Header>
        <Layout>
          <Sider width={160} theme="light" style={{ minHeight: 'calc(100vh - 56px)' }}>
            <Menu
              mode="inline"
              selectedKeys={[currentView]}
              items={menuItems}
              onClick={({ key }) => setCurrentView(key)}
              style={{
                height: '100%',
                borderRight: 0,
                fontSize: '14px'
              }}
            />
          </Sider>
          <Layout style={{ padding: '0 16px 16px' }}>
            <Content style={{
              padding: 16,
              margin: 0,
              minHeight: 'calc(100vh - 88px)',
              background: '#fff',
              borderRadius: '6px',
              boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
            }}>
              {renderContent()}
            </Content>
          </Layout>
        </Layout>
      </Layout>
    </ErrorBoundary>
  );
}

export default App;
