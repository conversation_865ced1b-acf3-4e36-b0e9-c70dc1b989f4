import React, { useState, useEffect } from 'react';
import { Layout, Typography, message, Menu } from 'antd';
import { 
  DashboardOutlined, 
  BarChartOutlined, 
  MonitorOutlined 
} from '@ant-design/icons';
import StrategyForm from './components/StrategyForm';
import ResultsDisplay from './components/ResultsDisplay';
import LoadingSpinner from './components/LoadingSpinner';
import ErrorBoundary from './components/ErrorBoundary';
import SystemMonitor from './components/SystemMonitor';
import { getStrategies, runBacktest } from './services/api';

const { Header, Content, Sider } = Layout;
const { Title } = Typography;

function App() {
  const [strategies, setStrategies] = useState([]);
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState(null);
  const [currentView, setCurrentView] = useState('backtest');

  useEffect(() => {
    loadStrategies();
  }, []);

  const loadStrategies = async () => {
    try {
      const data = await getStrategies();
      setStrategies(data);
    } catch (error) {
      message.error('加载策略失败: ' + error.message);
    }
  };

  const handleBacktest = async (formData) => {
    setLoading(true);
    setResults(null);
    
    try {
      message.loading('正在运行回测...', 0);
      const result = await runBacktest(formData);
      message.destroy();
      
      if (result.success) {
        setResults(result.data);
        message.success({
          content: '回测完成！',
          duration: 3,
        });
      } else {
        message.error({
          content: '回测失败: ' + result.error,
          duration: 5,
        });
      }
    } catch (error) {
      message.destroy();
      console.error('Backtest error:', error);
      message.error({
        content: '回测失败: ' + (error.message || '未知错误'),
        duration: 5,
      });
    } finally {
      setLoading(false);
    }
  };

  const menuItems = [
    {
      key: 'backtest',
      icon: <BarChartOutlined />,
      label: '策略回测',
    },
    {
      key: 'monitor',
      icon: <MonitorOutlined />,
      label: '系统监控',
    }
  ];

  const renderContent = () => {
    switch (currentView) {
      case 'monitor':
        return <SystemMonitor />;
      case 'backtest':
      default:
        return (
          <>
            <StrategyForm
              strategies={strategies}
              onSubmit={handleBacktest}
              loading={loading}
            />
            {loading && (
              <LoadingSpinner 
                message="正在运行回测，请稍候..." 
                size="large" 
              />
            )}
            {results && <ResultsDisplay results={results} />}
          </>
        );
    }
  };

  return (
    <ErrorBoundary>
      <Layout style={{ minHeight: '100vh' }}>
        <Header style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <DashboardOutlined style={{ fontSize: '24px', color: 'white', marginRight: '12px' }} />
            <Title level={4} style={{ color: 'white', margin: 0 }}>
              量化交易系统 v3.0
            </Title>
          </div>
        </Header>
        <Layout>
          <Sider width={200} theme="light">
            <Menu
              mode="inline"
              selectedKeys={[currentView]}
              items={menuItems}
              onClick={({ key }) => setCurrentView(key)}
              style={{ height: '100%', borderRight: 0 }}
            />
          </Sider>
          <Layout style={{ padding: '0 24px 24px' }}>
            <Content style={{ padding: 24, margin: 0, minHeight: 280, background: '#fff' }}>
              {renderContent()}
            </Content>
          </Layout>
        </Layout>
      </Layout>
    </ErrorBoundary>
  );
}

export default App;
