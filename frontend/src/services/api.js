import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    console.log('API Request:', config.method?.toUpperCase(), config.url);
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    console.log('API Response:', response.status, response.config.url);
    return response;
  },
  (error) => {
    console.error('API Error:', error.response?.status, error.message);
    return Promise.reject(error);
  }
);

// API方法
export const getStrategies = async () => {
  const response = await api.get('/strategies');
  return response.data;
};

export const getMarketData = async (params) => {
  const response = await api.post('/market-data', params);
  return response.data;
};

export const runBacktest = async (params) => {
  const response = await api.post('/backtest', params);
  return response.data;
};

export const healthCheck = async () => {
  const response = await api.get('/health');
  return response.data;
};

export const searchSymbols = async (query) => {
  const response = await api.get(`/symbols/search?query=${encodeURIComponent(query)}`);
  return response.data;
};

export const compareStrategies = async (symbol, strategies, startDate, endDate) => {
  const params = new URLSearchParams({
    symbol,
    strategies: strategies.join(','),
    ...(startDate && { start_date: startDate }),
    ...(endDate && { end_date: endDate })
  });

  const response = await api.get(`/performance/compare?${params}`);
  return response.data;
};

export const getSystemStatus = async () => {
  const response = await api.get('/system/status');
  return response.data;
};

export const getPerformanceMetrics = async () => {
  const response = await api.get('/monitoring/performance');
  return response.data;
};

export const getHealthCheck = async () => {
  const response = await api.get('/monitoring/health');
  return response.data;
};

export default api;
