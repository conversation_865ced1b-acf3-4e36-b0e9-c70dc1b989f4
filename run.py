#!/usr/bin/env python3
"""
量化交易系统 - 统一启动入口
"""

import os
import sys
import time
import signal
import subprocess
import threading
import argparse
import logging
from pathlib import Path

# 设置项目根目录
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class QuantTradingSystem:
    """量化交易系统启动器"""
    
    def __init__(self):
        self.backend_process = None
        self.frontend_process = None
        self.running = False
        
    def check_dependencies(self):
        """检查依赖"""
        logger.info("检查系统依赖...")
        
        # 检查Python依赖
        try:
            import fastapi
            import pandas
            import numpy
            import yfinance
            logger.info("✅ Python依赖检查通过")
        except ImportError as e:
            logger.error(f"❌ Python依赖缺失: {e}")
            logger.info("请运行: pip install -r requirements.txt")
            return False
            
        # 检查Node.js和npm
        try:
            result = subprocess.run(['node', '--version'], 
                                  capture_output=True, text=True, check=True)
            logger.info(f"✅ Node.js版本: {result.stdout.strip()}")
            
            result = subprocess.run(['npm', '--version'], 
                                  capture_output=True, text=True, check=True)
            logger.info(f"✅ npm版本: {result.stdout.strip()}")
        except (subprocess.CalledProcessError, FileNotFoundError):
            logger.error("❌ Node.js或npm未安装")
            logger.info("请安装Node.js: https://nodejs.org/")
            return False
            
        # 检查前端依赖
        frontend_dir = PROJECT_ROOT / 'frontend'
        node_modules = frontend_dir / 'node_modules'
        
        if not node_modules.exists():
            logger.info("安装前端依赖...")
            try:
                subprocess.run(['npm', 'install'], 
                             cwd=frontend_dir, check=True)
                logger.info("✅ 前端依赖安装完成")
            except subprocess.CalledProcessError:
                logger.error("❌ 前端依赖安装失败")
                return False
        else:
            logger.info("✅ 前端依赖已存在")
            
        return True
    
    def start_backend(self):
        """启动后端服务"""
        logger.info("启动后端服务...")
        
        backend_script = PROJECT_ROOT / 'scripts' / 'start_backend.py'
        if not backend_script.exists():
            # 直接启动FastAPI
            cmd = [
                sys.executable, '-m', 'uvicorn',
                'backend.main:app',
                '--host', '0.0.0.0',
                '--port', '8000',
                '--reload'
            ]
        else:
            cmd = [sys.executable, str(backend_script)]
            
        try:
            self.backend_process = subprocess.Popen(
                cmd,
                cwd=PROJECT_ROOT,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            logger.info("✅ 后端服务启动中...")
            return True
        except Exception as e:
            logger.error(f"❌ 后端启动失败: {e}")
            return False
    
    def start_frontend(self):
        """启动前端服务"""
        logger.info("启动前端服务...")
        
        frontend_dir = PROJECT_ROOT / 'frontend'
        
        try:
            self.frontend_process = subprocess.Popen(
                ['npm', 'start'],
                cwd=frontend_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            logger.info("✅ 前端服务启动中...")
            return True
        except Exception as e:
            logger.error(f"❌ 前端启动失败: {e}")
            return False
    
    def wait_for_services(self):
        """等待服务启动"""
        logger.info("等待服务启动...")
        
        # 等待后端
        import requests
        backend_ready = False
        for i in range(30):  # 等待30秒
            try:
                response = requests.get('http://localhost:8000/health', timeout=2)
                if response.status_code == 200:
                    backend_ready = True
                    logger.info("✅ 后端服务就绪")
                    break
            except:
                pass
            time.sleep(1)
        
        if not backend_ready:
            logger.warning("⚠️ 后端服务启动超时，但可能仍在启动中")
        
        # 前端通常需要更长时间
        logger.info("前端服务启动中，请稍候...")
        time.sleep(5)
        
        logger.info("🚀 系统启动完成!")
        logger.info("📱 前端地址: http://localhost:3000")
        logger.info("🔧 后端API: http://localhost:8000")
        logger.info("📚 API文档: http://localhost:8000/docs")
        logger.info("按 Ctrl+C 停止系统")
    
    def stop_services(self):
        """停止服务"""
        logger.info("正在停止服务...")
        
        if self.frontend_process:
            self.frontend_process.terminate()
            try:
                self.frontend_process.wait(timeout=5)
                logger.info("✅ 前端服务已停止")
            except subprocess.TimeoutExpired:
                self.frontend_process.kill()
                logger.info("🔪 强制停止前端服务")
        
        if self.backend_process:
            self.backend_process.terminate()
            try:
                self.backend_process.wait(timeout=5)
                logger.info("✅ 后端服务已停止")
            except subprocess.TimeoutExpired:
                self.backend_process.kill()
                logger.info("🔪 强制停止后端服务")
        
        self.running = False
        logger.info("🛑 系统已停止")
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        logger.info("收到停止信号...")
        self.stop_services()
        sys.exit(0)
    
    def run(self, mode='full'):
        """运行系统"""
        # 注册信号处理器
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        try:
            # 检查依赖
            if not self.check_dependencies():
                return False
            
            self.running = True
            
            if mode in ['full', 'backend']:
                if not self.start_backend():
                    return False
            
            if mode in ['full', 'frontend']:
                if not self.start_frontend():
                    return False
            
            if mode == 'full':
                self.wait_for_services()
            
            # 保持运行
            while self.running:
                time.sleep(1)
                
                # 检查进程状态
                if self.backend_process and self.backend_process.poll() is not None:
                    logger.error("后端进程意外退出")
                    break
                    
                if self.frontend_process and self.frontend_process.poll() is not None:
                    logger.error("前端进程意外退出")
                    break
            
        except KeyboardInterrupt:
            logger.info("用户中断")
        except Exception as e:
            logger.error(f"系统错误: {e}")
        finally:
            self.stop_services()
        
        return True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='量化交易系统启动器')
    parser.add_argument('--mode', choices=['full', 'backend', 'frontend'], 
                       default='full', help='启动模式')
    parser.add_argument('--check', action='store_true', help='仅检查依赖')
    
    args = parser.parse_args()
    
    system = QuantTradingSystem()
    
    if args.check:
        success = system.check_dependencies()
        sys.exit(0 if success else 1)
    
    logger.info("🚀 量化交易系统 v3.0")
    logger.info("=" * 50)
    
    success = system.run(args.mode)
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()
