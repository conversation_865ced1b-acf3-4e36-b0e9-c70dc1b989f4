# 项目结构说明

## 📁 目录结构

```
quantitative-trading-system/
├── 📁 backend/                    # FastAPI后端服务
│   └── main.py                   # 后端主程序
├── 📁 frontend/                  # React前端应用
│   ├── public/                   # 静态资源
│   ├── src/                      # 前端源码
│   │   ├── components/           # React组件
│   │   ├── services/            # API服务
│   │   └── App.js               # 主应用组件
│   └── package.json             # 前端依赖配置
├── 📁 src/                       # Python核心模块
│   ├── analytics/               # 性能分析模块
│   ├── backtest/               # 回测引擎
│   ├── data/                   # 数据管理
│   ├── monitoring/             # 系统监控
│   ├── strategy/               # 交易策略
│   └── utils/                  # 工具函数
├── 📁 scripts/                   # 启动和管理脚本
│   ├── start_backend.py        # 后端启动脚本
│   ├── start_frontend.sh       # 前端启动脚本
│   ├── start_system.sh         # 系统一键启动
│   ├── stop_system.sh          # 系统停止脚本
│   └── test_system.py          # 系统测试脚本
├── 📁 docs/                      # 项目文档
│   ├── LATEST_IMPROVEMENTS.md   # 最新改进记录
│   ├── FINAL_IMPROVEMENTS.md    # 历史改进记录
│   ├── IMPROVEMENTS.md          # 改进历史
│   ├── PROJECT_SUMMARY.md       # 项目总结
│   └── PROJECT_STRUCTURE.md     # 项目结构说明(本文件)
├── 📁 logs/                      # 系统日志
├── 📄 README.md                  # 项目说明文档
├── 📄 requirements.txt           # Python依赖
├── 📄 .gitignore                # Git忽略文件
├── 🚀 start.sh                  # 快速启动脚本
└── 🛑 stop.sh                   # 快速停止脚本
```

## 🎯 核心模块说明

### 后端模块 (backend/)
- **main.py**: FastAPI应用，提供RESTful API接口

### 前端模块 (frontend/)
- **components/**: React组件库
  - `StrategyForm.js`: 策略配置表单
  - `ResultsDisplay.js`: 结果展示组件
  - `SystemMonitor.js`: 系统监控面板
  - `ErrorBoundary.js`: 错误边界组件
  - `LoadingSpinner.js`: 加载指示器
  - `PerformanceChart.js`: 性能图表
- **services/**: API服务封装
  - `api.js`: 后端API调用封装

### 核心模块 (src/)

#### 📊 analytics/
- `dashboard.py`: 性能分析仪表板

#### 🔄 backtest/
- `backtester.py`: 回测引擎核心

#### 📈 data/
- `data_manager.py`: 数据获取和管理

#### 📋 monitoring/
- `health_check.py`: 系统健康检查

#### 🎯 strategy/
- `strategies.py`: 基础交易策略
- `advanced_strategies.py`: 高级交易策略
- `momentum_strategy.py`: 动量策略

#### 🛠️ utils/
- `config.py`: 配置管理
- `exceptions.py`: 自定义异常
- `helpers.py`: 辅助工具函数
- `json_utils.py`: JSON序列化工具
- `performance.py`: 性能监控工具
- `validators.py`: 数据验证工具

## 🚀 使用方式

### 快速启动
```bash
# 一键启动系统
./start.sh

# 停止系统
./stop.sh
```

### 详细操作
```bash
# 启动整个系统
./scripts/start_system.sh

# 分别启动
./scripts/start_backend.py    # 启动后端
./scripts/start_frontend.sh   # 启动前端

# 系统测试
./scripts/test_system.py

# 停止系统
./scripts/stop_system.sh
```

## 📝 开发指南

### 添加新策略
1. 在 `src/strategy/` 中实现策略类
2. 在 `backend/main.py` 中注册策略
3. 在前端添加策略参数配置

### 添加新功能模块
1. 在 `src/` 中创建新模块目录
2. 实现功能逻辑
3. 在 `src/__init__.py` 中导出
4. 在后端API中添加对应接口

### 测试和调试
- 使用 `./scripts/test_system.py` 进行系统测试
- 查看 `logs/` 目录中的日志文件
- 使用前端开发者工具调试界面问题

## 🔧 配置文件

- **requirements.txt**: Python依赖包列表
- **frontend/package.json**: Node.js依赖配置
- **src/utils/config.py**: 系统配置参数
- **.gitignore**: Git版本控制忽略文件

## 📊 监控和日志

- **logs/system.log**: 系统运行日志
- **logs/backend.log**: 后端服务日志
- **logs/frontend.log**: 前端构建日志
- **系统监控**: 通过前端界面查看系统状态
