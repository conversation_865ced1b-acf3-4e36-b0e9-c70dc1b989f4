# Backend dependencies
fastapi>=0.100.0
uvicorn>=0.22.0
pandas>=2.0.0
numpy>=1.24.0
yfinance>=0.2.28
python-dotenv>=1.0.0
ta>=0.11.0
pydantic>=2.0.0
python-multipart>=0.0.6

# Scientific computing and analysis
scipy>=1.10.0
scikit-learn>=1.3.0
matplotlib>=3.7.0
seaborn>=0.12.0

# Additional utilities
requests>=2.31.0
python-dateutil>=2.8.0
psutil>=5.9.0

# Testing and development
pytest>=7.0.0
pytest-asyncio>=0.21.0
pytest-cov>=4.0.0

# Security
cryptography>=41.0.0

# Async utilities
aiofiles>=23.0.0
asyncio-throttle>=1.0.0

# Performance monitoring
prometheus-client>=0.17.0