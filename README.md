# 量化交易系统 v3.0

现代化的前后端分离量化交易系统，提供专业的策略回测和分析功能。

## 架构特性

- **前后端分离**：React前端 + FastAPI后端
- **RESTful API**：标准化的API接口设计
- **现代化UI**：基于Ant Design的响应式界面
- **实时图表**：使用Recharts的交互式数据可视化
- **多种策略**：支持7种专业交易策略
- **高性能**：异步处理和优化的数据管理
- **易于扩展**：模块化设计，便于添加新功能

## 快速开始

### 方式一：一键启动（推荐）

```bash
./start.sh
```

或者使用详细脚本：
```bash
./scripts/start_system.sh
```

这将自动：
- 检查并安装所有依赖
- 同时启动前后端服务
- 提供实时状态监控

### 方式二：分别启动

#### 1. 安装后端依赖

```bash
pip install -r requirements.txt
```

#### 2. 启动后端服务

```bash
python scripts/start_backend.py
```

后端服务将在 http://localhost:8000 启动

#### 3. 安装前端依赖并启动

```bash
./scripts/start_frontend.sh
```

前端应用将在 http://localhost:3000 启动

#### 4. 访问应用

打开浏览器访问 http://localhost:3000 即可使用量化交易系统

### 停止系统

```bash
./stop.sh
```

或者使用详细脚本：
```bash
./scripts/stop_system.sh
```

或者按 Ctrl+C 停止一键启动的系统

## 功能特性

### 前端界面
- **策略配置**：直观的参数设置界面，支持实时参数验证
- **实时图表**：交互式性能图表和回撤分析
- **结果展示**：详细的回测统计和交易记录
- **一键复制**：便捷的结果复制功能
- **错误处理**：完善的错误边界和用户反馈
- **加载状态**：清晰的加载指示和进度反馈

### 后端API
- **RESTful设计**：标准化的API接口
- **异步处理**：高性能的数据处理
- **策略管理**：动态策略参数配置
- **数据缓存**：智能缓存机制，提升响应速度
- **参数验证**：完整的输入验证和错误处理
- **日志记录**：详细的操作日志和错误追踪

## 项目结构

```
quantitative-trading-system/
├── backend/
│   └── main.py        # FastAPI后端服务
├── frontend/
│   ├── src/
│   │   ├── components/    # React组件
│   │   ├── services/      # API服务
│   │   └── App.js         # 主应用
│   ├── public/
│   └── package.json       # 前端依赖
├── src/
│   ├── data/              # 数据获取和管理
│   ├── strategy/          # 交易策略
│   ├── backtest/          # 回测引擎
│   └── analytics/         # 性能分析
├── start_backend.py       # 后端启动脚本
├── start_frontend.sh      # 前端启动脚本
└── requirements.txt       # 后端依赖
```

## 可用策略

### 基础策略
1. **移动均线交叉**：快速均线上穿慢速均线时买入
2. **RSI策略**：超卖时买入，超买时卖出
3. **布林带**：基于价格触及通道边界进行交易
4. **买入持有**：简单的基准策略

### 高级策略
1. **MACD策略**：基于MACD指标的交易信号
2. **均值回归**：利用价格回归均值的特性
3. **VWAP策略**：基于成交量加权平均价

## 性能指标

回测系统计算以下指标：
- 总收益率
- 年化收益率
- 夏普比率
- 最大回撤
- 交易次数
- 胜率
- 盈亏比
- VaR和CVaR

## API接口

### 获取策略列表
```
GET /strategies
```

### 运行回测
```
POST /backtest
{
  "symbol": "AAPL",
  "strategy": "moving_average",
  "parameters": {
    "fast_period": 20,
    "slow_period": 50
  },
  "start_date": "2023-01-01",
  "end_date": "2024-01-01",
  "initial_capital": 10000
}
```

### 获取市场数据
```
POST /market-data
{
  "symbol": "AAPL",
  "start_date": "2023-01-01",
  "end_date": "2024-01-01"
}
```

## 系统要求

### 后端
- Python 3.8+
- FastAPI >= 0.100.0
- pandas >= 2.0.0
- numpy >= 1.24.0
- yfinance >= 0.2.28
- scipy >= 1.10.0
- scikit-learn >= 1.3.0

### 前端
- Node.js 16+
- React 18+
- Ant Design 5+
- Recharts 2+

### 系统环境
- macOS / Linux / Windows
- 至少 4GB RAM
- 网络连接（用于获取市场数据）

## 许可证

MIT

## 贡献

欢迎提交Pull Request！请保持代码简洁并附带完善的文档。

## 免责声明

本系统仅供教育和研究目的使用。请勿在没有适当风险管理和充分测试的情况下用于实际交易。