"""
Backtest engine for testing trading strategies
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)


class Backtester:
    """Simple backtesting engine"""
    
    def __init__(
        self,
        initial_capital: float = 100000,
        commission: float = 0.001,
        slippage: float = 0.001
    ):
        """
        Initialize backtester
        
        Args:
            initial_capital: Starting capital
            commission: Commission rate (e.g., 0.001 = 0.1%)
            slippage: Slippage rate
        """
        self.initial_capital = initial_capital
        self.commission = commission
        self.slippage = slippage
        self.results = {}
    
    def run(
        self,
        strategy: Any,
        data: pd.DataFrame,
        position_size: float = 1.0
    ) -> Dict[str, Any]:
        """
        Run backtest
        
        Args:
            strategy: Strategy object with generate_signals method
            data: DataFrame with OHLCV data
            position_size: Position size as fraction of capital (1.0 = 100%)
        
        Returns:
            Dictionary with backtest results
        """
        if data.empty:
            logger.error("No data provided for backtest")
            return {}
        
        # Generate signals
        signals = strategy.generate_signals(data)
        
        # Initialize portfolio with proper data types
        portfolio = pd.DataFrame(index=data.index)
        portfolio['price'] = data['close'].astype(float)
        portfolio['signal'] = signals.astype(int)
        portfolio['position'] = 0.0
        portfolio['cash'] = float(self.initial_capital)
        portfolio['holdings'] = 0.0
        portfolio['total'] = float(self.initial_capital)
        portfolio['returns'] = 0.0
        
        # Track trades
        trades = []
        current_position = 0
        current_cash = self.initial_capital
        buy_price = 0  # Track buy price for PnL calculation
        
        for i in range(len(portfolio)):
            if i == 0:
                continue
            
            price = portfolio['price'].iloc[i]
            signal = portfolio['signal'].iloc[i]
            
            # Process signals
            if signal == 1 and current_position == 0:  # Buy
                # Calculate position size
                position_value = current_cash * position_size
                shares = int(position_value / price)
                
                if shares > 0:
                    cost = shares * price * (1 + self.slippage)
                    commission_cost = cost * self.commission
                    total_cost = cost + commission_cost
                    
                    if total_cost <= current_cash:
                        current_cash -= total_cost
                        current_position = shares
                        buy_price = price * (1 + self.slippage)  # Include slippage in buy price

                        trades.append({
                            'date': portfolio.index[i],
                            'type': 'BUY',
                            'price': price,
                            'shares': shares,
                            'cost': total_cost,
                            'pnl': 0  # Buy trades have no PnL
                        })
            
            elif signal == -1 and current_position > 0:  # Sell
                revenue = current_position * price * (1 - self.slippage)
                commission_cost = revenue * self.commission
                total_revenue = revenue - commission_cost

                # Calculate PnL for this trade
                total_cost = current_position * buy_price + (current_position * buy_price * self.commission)
                pnl = total_revenue - total_cost

                current_cash += total_revenue

                trades.append({
                    'date': portfolio.index[i],
                    'type': 'SELL',
                    'price': price,
                    'shares': current_position,
                    'revenue': total_revenue,
                    'pnl': pnl
                })

                current_position = 0
                buy_price = 0
            
            # Update portfolio using .loc to avoid pandas warnings
            portfolio.loc[portfolio.index[i], 'position'] = current_position
            portfolio.loc[portfolio.index[i], 'cash'] = float(current_cash)
            portfolio.loc[portfolio.index[i], 'holdings'] = float(current_position * price)
            portfolio.loc[portfolio.index[i], 'total'] = float(current_cash + (current_position * price))
        
        # Calculate returns
        portfolio['returns'] = portfolio['total'].pct_change()
        
        # Calculate metrics
        results = self._calculate_metrics(portfolio, trades)
        results['portfolio'] = portfolio
        results['trades'] = trades
        
        self.results = results
        return results
    
    def _calculate_metrics(
        self,
        portfolio: pd.DataFrame,
        trades: list
    ) -> Dict[str, Any]:
        """Calculate performance metrics"""
        total_return = (portfolio['total'].iloc[-1] - self.initial_capital) / self.initial_capital
        
        # Calculate annualized return
        days = len(portfolio)
        years = days / 252
        annualized_return = ((1 + total_return) ** (1 / years) - 1) if years > 0 else 0
        
        # Calculate Sharpe ratio
        returns = portfolio['returns'].dropna()
        if len(returns) > 0:
            sharpe_ratio = (returns.mean() / returns.std() * np.sqrt(252)) if returns.std() > 0 else 0
        else:
            sharpe_ratio = 0
        
        # Calculate max drawdown
        cumulative = (1 + returns).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        max_drawdown = drawdown.min()
        
        # Trade statistics
        num_trades = len(trades)
        buy_trades = [t for t in trades if t['type'] == 'BUY']
        sell_trades = [t for t in trades if t['type'] == 'SELL']
        
        metrics = {
            'initial_capital': self.initial_capital,
            'final_value': portfolio['total'].iloc[-1],
            'total_return': total_return,
            'annualized_return': annualized_return,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'num_trades': num_trades,
            'num_buy_trades': len(buy_trades),
            'num_sell_trades': len(sell_trades),
        }
        
        return metrics
    
    def plot_results(self, show: bool = True):
        """Plot backtest results"""
        if not self.results or 'portfolio' not in self.results:
            logger.error("No results to plot")
            return
        
        import matplotlib.pyplot as plt
        
        portfolio = self.results['portfolio']
        
        fig, axes = plt.subplots(3, 1, figsize=(12, 10))
        
        # Plot portfolio value
        axes[0].plot(portfolio.index, portfolio['total'], label='Portfolio Value')
        axes[0].axhline(y=self.initial_capital, color='r', linestyle='--', label='Initial Capital')
        axes[0].set_ylabel('Portfolio Value ($)')
        axes[0].set_title('Portfolio Performance')
        axes[0].legend()
        axes[0].grid(True)
        
        # Plot price and signals
        axes[1].plot(portfolio.index, portfolio['price'], label='Price', alpha=0.7)
        
        # Mark buy signals
        buy_signals = portfolio[portfolio['signal'] == 1]
        if not buy_signals.empty:
            axes[1].scatter(buy_signals.index, buy_signals['price'], 
                          color='green', marker='^', s=100, label='Buy')
        
        # Mark sell signals
        sell_signals = portfolio[portfolio['signal'] == -1]
        if not sell_signals.empty:
            axes[1].scatter(sell_signals.index, sell_signals['price'], 
                          color='red', marker='v', s=100, label='Sell')
        
        axes[1].set_ylabel('Price ($)')
        axes[1].set_title('Price and Trading Signals')
        axes[1].legend()
        axes[1].grid(True)
        
        # Plot returns
        axes[2].plot(portfolio.index, portfolio['returns'].cumsum(), label='Cumulative Returns')
        axes[2].set_ylabel('Cumulative Returns')
        axes[2].set_xlabel('Date')
        axes[2].set_title('Cumulative Returns')
        axes[2].legend()
        axes[2].grid(True)
        
        plt.tight_layout()
        
        if show:
            plt.show()
        
        return fig
    
    def print_summary(self):
        """Print summary of backtest results"""
        if not self.results:
            logger.error("No results to display")
            return
        
        metrics = self.results
        
        print("\n" + "="*50)
        print("BACKTEST RESULTS")
        print("="*50)
        print(f"Initial Capital:     ${metrics['initial_capital']:,.2f}")
        print(f"Final Value:         ${metrics['final_value']:,.2f}")
        print(f"Total Return:        {metrics['total_return']:.2%}")
        print(f"Annualized Return:   {metrics['annualized_return']:.2%}")
        print(f"Sharpe Ratio:        {metrics['sharpe_ratio']:.2f}")
        print(f"Max Drawdown:        {metrics['max_drawdown']:.2%}")
        print(f"Number of Trades:    {metrics['num_trades']}")
        print("="*50)