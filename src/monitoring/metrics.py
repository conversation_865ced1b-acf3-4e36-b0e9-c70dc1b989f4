"""
增强的系统监控和指标收集模块
"""
import time
import psutil
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from collections import defaultdict, deque
import json
import logging
from dataclasses import dataclass, asdict
from pathlib import Path

from ..utils.config import PROJECT_ROOT


@dataclass
class MetricPoint:
    """指标数据点"""
    timestamp: datetime
    value: float
    tags: Dict[str, str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'timestamp': self.timestamp.isoformat(),
            'value': self.value,
            'tags': self.tags or {}
        }


class MetricCollector:
    """指标收集器"""
    
    def __init__(self, max_points: int = 1000):
        self.max_points = max_points
        self.metrics = defaultdict(lambda: deque(maxlen=max_points))
        self.counters = defaultdict(float)
        self.gauges = defaultdict(float)
        self.histograms = defaultdict(list)
        self._lock = threading.RLock()
        self.logger = logging.getLogger(__name__)
    
    def counter(self, name: str, value: float = 1.0, tags: Dict[str, str] = None) -> None:
        """计数器指标"""
        with self._lock:
            key = self._make_key(name, tags)
            self.counters[key] += value
            self._add_point(name, self.counters[key], tags)
    
    def gauge(self, name: str, value: float, tags: Dict[str, str] = None) -> None:
        """仪表盘指标"""
        with self._lock:
            key = self._make_key(name, tags)
            self.gauges[key] = value
            self._add_point(name, value, tags)
    
    def histogram(self, name: str, value: float, tags: Dict[str, str] = None) -> None:
        """直方图指标"""
        with self._lock:
            key = self._make_key(name, tags)
            self.histograms[key].append(value)
            # 保持最近1000个值
            if len(self.histograms[key]) > 1000:
                self.histograms[key] = self.histograms[key][-1000:]
            self._add_point(name, value, tags)
    
    def timer(self, name: str, tags: Dict[str, str] = None):
        """计时器上下文管理器"""
        return TimerContext(self, name, tags)
    
    def _make_key(self, name: str, tags: Dict[str, str] = None) -> str:
        """生成指标键"""
        if not tags:
            return name
        
        tag_str = ','.join(f"{k}={v}" for k, v in sorted(tags.items()))
        return f"{name}[{tag_str}]"
    
    def _add_point(self, name: str, value: float, tags: Dict[str, str] = None) -> None:
        """添加数据点"""
        point = MetricPoint(datetime.now(), value, tags)
        self.metrics[name].append(point)
    
    def get_metrics(self, name: str = None, since: datetime = None) -> Dict[str, List[Dict]]:
        """获取指标数据"""
        with self._lock:
            result = {}
            
            metrics_to_get = [name] if name else self.metrics.keys()
            
            for metric_name in metrics_to_get:
                if metric_name in self.metrics:
                    points = self.metrics[metric_name]
                    
                    if since:
                        points = [p for p in points if p.timestamp >= since]
                    
                    result[metric_name] = [p.to_dict() for p in points]
            
            return result
    
    def get_summary(self) -> Dict[str, Any]:
        """获取指标摘要"""
        with self._lock:
            summary = {
                'timestamp': datetime.now().isoformat(),
                'counters': dict(self.counters),
                'gauges': dict(self.gauges),
                'histograms': {}
            }
            
            # 计算直方图统计
            for key, values in self.histograms.items():
                if values:
                    summary['histograms'][key] = {
                        'count': len(values),
                        'min': min(values),
                        'max': max(values),
                        'mean': sum(values) / len(values),
                        'p50': self._percentile(values, 50),
                        'p95': self._percentile(values, 95),
                        'p99': self._percentile(values, 99)
                    }
            
            return summary
    
    def _percentile(self, values: List[float], percentile: float) -> float:
        """计算百分位数"""
        if not values:
            return 0.0
        
        sorted_values = sorted(values)
        index = int(len(sorted_values) * percentile / 100)
        return sorted_values[min(index, len(sorted_values) - 1)]
    
    def clear(self) -> None:
        """清空所有指标"""
        with self._lock:
            self.metrics.clear()
            self.counters.clear()
            self.gauges.clear()
            self.histograms.clear()


class TimerContext:
    """计时器上下文管理器"""
    
    def __init__(self, collector: MetricCollector, name: str, tags: Dict[str, str] = None):
        self.collector = collector
        self.name = name
        self.tags = tags
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time:
            duration = time.time() - self.start_time
            self.collector.histogram(self.name, duration, self.tags)


class SystemMonitor:
    """系统监控器"""
    
    def __init__(self, collector: MetricCollector, interval: float = 10.0):
        self.collector = collector
        self.interval = interval
        self.running = False
        self.thread = None
        self.logger = logging.getLogger(__name__)
    
    def start(self) -> None:
        """启动监控"""
        if self.running:
            return
        
        self.running = True
        self.thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.thread.start()
        self.logger.info("System monitoring started")
    
    def stop(self) -> None:
        """停止监控"""
        self.running = False
        if self.thread:
            self.thread.join(timeout=5.0)
        self.logger.info("System monitoring stopped")
    
    def _monitor_loop(self) -> None:
        """监控循环"""
        while self.running:
            try:
                self._collect_system_metrics()
                time.sleep(self.interval)
            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}")
                time.sleep(self.interval)
    
    def _collect_system_metrics(self) -> None:
        """收集系统指标"""
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        self.collector.gauge('system.cpu.usage', cpu_percent, {'unit': 'percent'})
        
        # 内存使用情况
        memory = psutil.virtual_memory()
        self.collector.gauge('system.memory.usage', memory.percent, {'unit': 'percent'})
        self.collector.gauge('system.memory.available', memory.available / 1024**3, {'unit': 'GB'})
        
        # 磁盘使用情况
        disk = psutil.disk_usage('/')
        self.collector.gauge('system.disk.usage', disk.percent, {'unit': 'percent'})
        self.collector.gauge('system.disk.free', disk.free / 1024**3, {'unit': 'GB'})
        
        # 网络IO
        net_io = psutil.net_io_counters()
        self.collector.counter('system.network.bytes_sent', net_io.bytes_sent, {'direction': 'out'})
        self.collector.counter('system.network.bytes_recv', net_io.bytes_recv, {'direction': 'in'})
        
        # 进程数量
        process_count = len(psutil.pids())
        self.collector.gauge('system.processes.count', process_count)


class ApplicationMonitor:
    """应用程序监控器"""
    
    def __init__(self, collector: MetricCollector):
        self.collector = collector
        self.start_time = datetime.now()
        self.request_count = 0
        self.error_count = 0
        self.active_connections = 0
    
    def record_request(self, endpoint: str, method: str, status_code: int, duration: float) -> None:
        """记录请求"""
        self.request_count += 1
        
        tags = {
            'endpoint': endpoint,
            'method': method,
            'status_code': str(status_code)
        }
        
        self.collector.counter('app.requests.total', 1, tags)
        self.collector.histogram('app.requests.duration', duration, tags)
        
        if status_code >= 400:
            self.error_count += 1
            self.collector.counter('app.errors.total', 1, tags)
    
    def record_database_query(self, query_type: str, duration: float, success: bool = True) -> None:
        """记录数据库查询"""
        tags = {
            'query_type': query_type,
            'success': str(success)
        }
        
        self.collector.counter('app.db.queries.total', 1, tags)
        self.collector.histogram('app.db.queries.duration', duration, tags)
    
    def record_cache_operation(self, operation: str, hit: bool = True) -> None:
        """记录缓存操作"""
        tags = {
            'operation': operation,
            'result': 'hit' if hit else 'miss'
        }
        
        self.collector.counter('app.cache.operations.total', 1, tags)
    
    def update_active_connections(self, count: int) -> None:
        """更新活跃连接数"""
        self.active_connections = count
        self.collector.gauge('app.connections.active', count)
    
    def get_app_stats(self) -> Dict[str, Any]:
        """获取应用统计"""
        uptime = (datetime.now() - self.start_time).total_seconds()
        
        return {
            'uptime_seconds': uptime,
            'total_requests': self.request_count,
            'total_errors': self.error_count,
            'error_rate': self.error_count / self.request_count if self.request_count > 0 else 0,
            'active_connections': self.active_connections,
            'requests_per_second': self.request_count / uptime if uptime > 0 else 0
        }


class MetricsExporter:
    """指标导出器"""
    
    def __init__(self, collector: MetricCollector, export_dir: Optional[Path] = None):
        self.collector = collector
        self.export_dir = export_dir or PROJECT_ROOT / 'metrics'
        self.export_dir.mkdir(exist_ok=True)
        self.logger = logging.getLogger(__name__)
    
    def export_to_json(self, filename: Optional[str] = None) -> str:
        """导出为JSON文件"""
        if not filename:
            filename = f"metrics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        filepath = self.export_dir / filename
        
        data = {
            'export_time': datetime.now().isoformat(),
            'summary': self.collector.get_summary(),
            'metrics': self.collector.get_metrics()
        }
        
        with open(filepath, 'w') as f:
            json.dump(data, f, indent=2, default=str)
        
        self.logger.info(f"Metrics exported to {filepath}")
        return str(filepath)
    
    def export_prometheus_format(self) -> str:
        """导出为Prometheus格式"""
        lines = []
        summary = self.collector.get_summary()
        
        # 计数器
        for name, value in summary['counters'].items():
            lines.append(f"# TYPE {name} counter")
            lines.append(f"{name} {value}")
        
        # 仪表盘
        for name, value in summary['gauges'].items():
            lines.append(f"# TYPE {name} gauge")
            lines.append(f"{name} {value}")
        
        # 直方图
        for name, stats in summary['histograms'].items():
            lines.append(f"# TYPE {name} histogram")
            for stat, value in stats.items():
                lines.append(f"{name}_{stat} {value}")
        
        return '\n'.join(lines)


# 全局指标收集器和监控器
metrics_collector = MetricCollector()
system_monitor = SystemMonitor(metrics_collector)
app_monitor = ApplicationMonitor(metrics_collector)
metrics_exporter = MetricsExporter(metrics_collector)


def track_performance(name: str, tags: Dict[str, str] = None):
    """性能跟踪装饰器"""
    def decorator(func: Callable) -> Callable:
        def wrapper(*args, **kwargs):
            with metrics_collector.timer(f"performance.{name}", tags):
                return func(*args, **kwargs)
        return wrapper
    return decorator


def track_errors(error_metric: str = "errors.total"):
    """错误跟踪装饰器"""
    def decorator(func: Callable) -> Callable:
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                metrics_collector.counter(error_metric, 1, {
                    'function': func.__name__,
                    'error_type': type(e).__name__
                })
                raise
        return wrapper
    return decorator
