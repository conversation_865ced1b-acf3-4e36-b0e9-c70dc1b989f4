"""
系统健康检查模块
"""

import psutil
import requests
import yfinance as yf
from datetime import datetime, timedelta
from typing import Dict, List, Any
import logging
import sys
import os

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(__file__))))

from utils.performance import performance_monitor

logger = logging.getLogger(__name__)

class HealthChecker:
    """系统健康检查器"""
    
    def __init__(self):
        self.checks = {
            'system': self.check_system_resources,
            'data_source': self.check_data_source,
            'api_endpoints': self.check_api_endpoints,
            'dependencies': self.check_dependencies
        }
    
    def check_system_resources(self) -> Dict[str, Any]:
        """检查系统资源"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            status = "healthy"
            issues = []
            
            if cpu_percent > 80:
                status = "warning"
                issues.append(f"CPU使用率过高: {cpu_percent}%")
            
            if memory.percent > 85:
                status = "warning"
                issues.append(f"内存使用率过高: {memory.percent}%")
            
            if disk.percent > 90:
                status = "warning"
                issues.append(f"磁盘使用率过高: {disk.percent}%")
            
            return {
                'status': status,
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_available_gb': memory.available / (1024**3),
                'disk_percent': disk.percent,
                'disk_free_gb': disk.free / (1024**3),
                'issues': issues,
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def check_data_source(self) -> Dict[str, Any]:
        """检查数据源连接"""
        try:
            # 测试获取少量数据
            ticker = yf.Ticker('AAPL')
            data = ticker.history(period='1d')
            
            if data.empty:
                return {
                    'status': 'error',
                    'message': '无法获取市场数据',
                    'timestamp': datetime.now().isoformat()
                }
            
            return {
                'status': 'healthy',
                'message': '数据源连接正常',
                'last_data_point': data.index[-1].isoformat() if not data.empty else None,
                'data_points': len(data),
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def check_api_endpoints(self, base_url: str = "http://localhost:8000") -> Dict[str, Any]:
        """检查API端点"""
        endpoints = [
            '/health',
            '/strategies',
            '/system/status'
        ]
        
        results = {}
        overall_status = 'healthy'
        
        for endpoint in endpoints:
            try:
                response = requests.get(f"{base_url}{endpoint}", timeout=5)
                if response.status_code == 200:
                    results[endpoint] = {
                        'status': 'healthy',
                        'response_time': response.elapsed.total_seconds(),
                        'status_code': response.status_code
                    }
                else:
                    results[endpoint] = {
                        'status': 'error',
                        'status_code': response.status_code,
                        'error': f"HTTP {response.status_code}"
                    }
                    overall_status = 'error'
            except requests.exceptions.RequestException as e:
                results[endpoint] = {
                    'status': 'error',
                    'error': str(e)
                }
                overall_status = 'error'
        
        return {
            'status': overall_status,
            'endpoints': results,
            'timestamp': datetime.now().isoformat()
        }
    
    def check_dependencies(self) -> Dict[str, Any]:
        """检查依赖包"""
        required_packages = [
            'fastapi', 'uvicorn', 'pandas', 'numpy', 
            'yfinance', 'ta', 'scipy', 'scikit-learn'
        ]
        
        results = {}
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package)
                results[package] = {'status': 'installed'}
            except ImportError:
                results[package] = {'status': 'missing'}
                missing_packages.append(package)
        
        status = 'healthy' if not missing_packages else 'error'
        
        return {
            'status': status,
            'packages': results,
            'missing_packages': missing_packages,
            'timestamp': datetime.now().isoformat()
        }
    
    def run_all_checks(self, api_base_url: str = "http://localhost:8000") -> Dict[str, Any]:
        """运行所有健康检查"""
        results = {}
        overall_status = 'healthy'
        
        for check_name, check_func in self.checks.items():
            try:
                if check_name == 'api_endpoints':
                    result = check_func(api_base_url)
                else:
                    result = check_func()
                
                results[check_name] = result
                
                if result.get('status') != 'healthy':
                    overall_status = 'warning' if overall_status == 'healthy' else 'error'
                    
            except Exception as e:
                results[check_name] = {
                    'status': 'error',
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                }
                overall_status = 'error'
        
        return {
            'overall_status': overall_status,
            'checks': results,
            'timestamp': datetime.now().isoformat(),
            'system_info': performance_monitor.get_system_info()
        }

# 全局健康检查器实例
health_checker = HealthChecker()
