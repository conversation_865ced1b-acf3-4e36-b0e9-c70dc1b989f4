import pandas as pd
import numpy as np
from typing import Op<PERSON>, <PERSON><PERSON>
from abc import ABC, abstractmethod


class BaseAdvancedStrategy(ABC):
    def __init__(self, name: str = "AdvancedStrategy"):
        self.name = name
        
    @abstractmethod
    def generate_signals(self, data: pd.DataFrame) -> pd.Series:
        pass


class MeanReversionStrategy(BaseAdvancedStrategy):
    def __init__(self, lookback: int = 20, z_threshold: float = 2.0):
        super().__init__("MeanReversion")
        self.lookback = lookback
        self.z_threshold = z_threshold
    
    def generate_signals(self, data: pd.DataFrame) -> pd.Series:
        signals = pd.Series(index=data.index, data=0)
        
        mean = data['close'].rolling(window=self.lookback).mean()
        std = data['close'].rolling(window=self.lookback).std()
        z_score = (data['close'] - mean) / std
        
        signals[z_score < -self.z_threshold] = 1
        signals[z_score > self.z_threshold] = -1
        
        return signals


class MomentumStrategy(BaseAdvancedStrategy):
    def __init__(self, fast_window: int = 10, slow_window: int = 30):
        super().__init__("Momentum")
        self.fast_window = fast_window
        self.slow_window = slow_window
    
    def generate_signals(self, data: pd.DataFrame) -> pd.Series:
        signals = pd.Series(index=data.index, data=0)
        
        fast_momentum = data['close'].pct_change(self.fast_window)
        slow_momentum = data['close'].pct_change(self.slow_window)
        
        signals[(fast_momentum > 0) & (slow_momentum > 0)] = 1
        signals[(fast_momentum < 0) & (slow_momentum < 0)] = -1
        
        return signals


class VWAPStrategy(BaseAdvancedStrategy):
    def __init__(self, window: int = 20):
        super().__init__("VWAP")
        self.window = window
    
    def generate_signals(self, data: pd.DataFrame) -> pd.Series:
        signals = pd.Series(index=data.index, data=0)
        
        typical_price = (data['high'] + data['low'] + data['close']) / 3
        vwap = (typical_price * data['volume']).rolling(window=self.window).sum() / \
               data['volume'].rolling(window=self.window).sum()
        
        signals[data['close'] > vwap * 1.01] = 1
        signals[data['close'] < vwap * 0.99] = -1
        
        return signals


class StochasticOscillator(BaseAdvancedStrategy):
    def __init__(self, k_period: int = 14, d_period: int = 3, oversold: float = 20, overbought: float = 80):
        super().__init__("Stochastic")
        self.k_period = k_period
        self.d_period = d_period
        self.oversold = oversold
        self.overbought = overbought
    
    def generate_signals(self, data: pd.DataFrame) -> pd.Series:
        signals = pd.Series(index=data.index, data=0)
        
        low_min = data['low'].rolling(window=self.k_period).min()
        high_max = data['high'].rolling(window=self.k_period).max()
        
        k_percent = 100 * ((data['close'] - low_min) / (high_max - low_min))
        d_percent = k_percent.rolling(window=self.d_period).mean()
        
        signals[(k_percent < self.oversold) & (d_percent < self.oversold)] = 1
        signals[(k_percent > self.overbought) & (d_percent > self.overbought)] = -1
        
        return signals


class MACDStrategy(BaseAdvancedStrategy):
    def __init__(self, fast: int = 12, slow: int = 26, signal: int = 9):
        super().__init__("MACD")
        self.fast = fast
        self.slow = slow
        self.signal = signal
    
    def generate_signals(self, data: pd.DataFrame) -> pd.Series:
        signals = pd.Series(index=data.index, data=0)
        
        exp1 = data['close'].ewm(span=self.fast, adjust=False).mean()
        exp2 = data['close'].ewm(span=self.slow, adjust=False).mean()
        
        macd = exp1 - exp2
        signal_line = macd.ewm(span=self.signal, adjust=False).mean()
        
        signals[macd > signal_line] = 1
        signals[macd < signal_line] = -1
        
        return signals


class ATRTrailingStop(BaseAdvancedStrategy):
    def __init__(self, atr_period: int = 14, atr_multiplier: float = 2.0):
        super().__init__("ATRTrailingStop")
        self.atr_period = atr_period
        self.atr_multiplier = atr_multiplier
    
    def calculate_atr(self, data: pd.DataFrame) -> pd.Series:
        high_low = data['high'] - data['low']
        high_close = np.abs(data['high'] - data['close'].shift())
        low_close = np.abs(data['low'] - data['close'].shift())
        
        ranges = pd.concat([high_low, high_close, low_close], axis=1)
        true_range = ranges.max(axis=1)
        
        return true_range.rolling(window=self.atr_period).mean()
    
    def generate_signals(self, data: pd.DataFrame) -> pd.Series:
        signals = pd.Series(index=data.index, data=0)
        atr = self.calculate_atr(data)
        
        trailing_stop_long = data['close'] - (atr * self.atr_multiplier)
        trailing_stop_short = data['close'] + (atr * self.atr_multiplier)
        
        position = 0
        for i in range(1, len(data)):
            if position == 0:
                if data['close'].iloc[i] > trailing_stop_long.iloc[i]:
                    position = 1
                    signals.iloc[i] = 1
                elif data['close'].iloc[i] < trailing_stop_short.iloc[i]:
                    position = -1
                    signals.iloc[i] = -1
            elif position == 1:
                if data['close'].iloc[i] < trailing_stop_long.iloc[i]:
                    position = 0
                    signals.iloc[i] = -1
            elif position == -1:
                if data['close'].iloc[i] > trailing_stop_short.iloc[i]:
                    position = 0
                    signals.iloc[i] = 1
        
        return signals


class CombinedStrategy(BaseAdvancedStrategy):
    def __init__(self, strategies: list, weights: Optional[list] = None):
        super().__init__("Combined")
        self.strategies = strategies
        self.weights = weights or [1/len(strategies)] * len(strategies)
    
    def generate_signals(self, data: pd.DataFrame) -> pd.Series:
        all_signals = []
        
        for strategy in self.strategies:
            signals = strategy.generate_signals(data)
            all_signals.append(signals)
        
        weighted_signals = pd.DataFrame(all_signals).T
        final_signals = (weighted_signals * self.weights).sum(axis=1)
        
        signals = pd.Series(index=data.index, data=0)
        signals[final_signals > 0.5] = 1
        signals[final_signals < -0.5] = -1
        
        return signals