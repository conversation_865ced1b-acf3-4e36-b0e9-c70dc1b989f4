"""
高级缓存管理模块
"""
import hashlib
import json
import pickle
import time
from datetime import datetime, timedelta
from typing import Any, Dict, Optional, Callable
from functools import wraps
import threading
import os
from pathlib import Path

from .config import PROJECT_ROOT, CACHE_TTL


class CacheManager:
    """高级缓存管理器"""
    
    def __init__(self, cache_dir: Optional[str] = None, default_ttl: int = CACHE_TTL):
        self.cache_dir = Path(cache_dir) if cache_dir else PROJECT_ROOT / 'cache'
        self.cache_dir.mkdir(exist_ok=True)
        self.default_ttl = default_ttl
        self.memory_cache = {}
        self.cache_stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0
        }
        self._lock = threading.RLock()
    
    def _generate_key(self, key_data: Any) -> str:
        """生成缓存键"""
        if isinstance(key_data, str):
            return hashlib.md5(key_data.encode()).hexdigest()
        else:
            serialized = json.dumps(key_data, sort_keys=True, default=str)
            return hashlib.md5(serialized.encode()).hexdigest()
    
    def _is_expired(self, cache_entry: Dict) -> bool:
        """检查缓存是否过期"""
        if 'expires_at' not in cache_entry:
            return False
        return datetime.now() > cache_entry['expires_at']
    
    def get(self, key: Any, default: Any = None) -> Any:
        """获取缓存值"""
        with self._lock:
            cache_key = self._generate_key(key)
            
            # 首先检查内存缓存
            if cache_key in self.memory_cache:
                entry = self.memory_cache[cache_key]
                if not self._is_expired(entry):
                    self.cache_stats['hits'] += 1
                    return entry['value']
                else:
                    del self.memory_cache[cache_key]
            
            # 检查磁盘缓存
            cache_file = self.cache_dir / f"{cache_key}.cache"
            if cache_file.exists():
                try:
                    with open(cache_file, 'rb') as f:
                        entry = pickle.load(f)
                    
                    if not self._is_expired(entry):
                        # 加载到内存缓存
                        self.memory_cache[cache_key] = entry
                        self.cache_stats['hits'] += 1
                        return entry['value']
                    else:
                        cache_file.unlink()  # 删除过期文件
                except Exception:
                    cache_file.unlink()  # 删除损坏的缓存文件
            
            self.cache_stats['misses'] += 1
            return default
    
    def set(self, key: Any, value: Any, ttl: Optional[int] = None) -> None:
        """设置缓存值"""
        with self._lock:
            cache_key = self._generate_key(key)
            ttl = ttl or self.default_ttl
            
            entry = {
                'value': value,
                'created_at': datetime.now(),
                'expires_at': datetime.now() + timedelta(seconds=ttl) if ttl > 0 else None
            }
            
            # 存储到内存缓存
            self.memory_cache[cache_key] = entry
            
            # 存储到磁盘缓存
            cache_file = self.cache_dir / f"{cache_key}.cache"
            try:
                with open(cache_file, 'wb') as f:
                    pickle.dump(entry, f)
            except Exception as e:
                print(f"Failed to write cache to disk: {e}")
            
            self.cache_stats['sets'] += 1
    
    def delete(self, key: Any) -> bool:
        """删除缓存项"""
        with self._lock:
            cache_key = self._generate_key(key)
            deleted = False
            
            # 从内存缓存删除
            if cache_key in self.memory_cache:
                del self.memory_cache[cache_key]
                deleted = True
            
            # 从磁盘缓存删除
            cache_file = self.cache_dir / f"{cache_key}.cache"
            if cache_file.exists():
                cache_file.unlink()
                deleted = True
            
            if deleted:
                self.cache_stats['deletes'] += 1
            
            return deleted
    
    def clear(self) -> None:
        """清空所有缓存"""
        with self._lock:
            # 清空内存缓存
            self.memory_cache.clear()
            
            # 清空磁盘缓存
            for cache_file in self.cache_dir.glob("*.cache"):
                cache_file.unlink()
            
            # 重置统计
            self.cache_stats = {
                'hits': 0,
                'misses': 0,
                'sets': 0,
                'deletes': 0
            }
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        total_requests = self.cache_stats['hits'] + self.cache_stats['misses']
        hit_rate = self.cache_stats['hits'] / total_requests if total_requests > 0 else 0
        
        return {
            **self.cache_stats,
            'hit_rate': hit_rate,
            'memory_cache_size': len(self.memory_cache),
            'disk_cache_files': len(list(self.cache_dir.glob("*.cache")))
        }


# 全局缓存管理器实例
cache_manager = CacheManager()


def cached(ttl: int = None, key_func: Callable = None):
    """缓存装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = {
                    'func': func.__name__,
                    'args': args,
                    'kwargs': kwargs
                }
            
            # 尝试从缓存获取
            result = cache_manager.get(cache_key)
            if result is not None:
                return result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            cache_manager.set(cache_key, result, ttl)
            return result
        
        return wrapper
    return decorator


def cache_clear():
    """清空缓存的便捷函数"""
    cache_manager.clear()


def cache_stats():
    """获取缓存统计的便捷函数"""
    return cache_manager.get_stats()
