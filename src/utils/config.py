"""
配置管理模块
"""

import os
import logging
from typing import Dict, Any
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent.parent

# 日志配置
LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

# 数据配置
DATA_CACHE_SIZE = int(os.getenv('DATA_CACHE_SIZE', '100'))
DEFAULT_LOOKBACK_DAYS = int(os.getenv('DEFAULT_LOOKBACK_DAYS', '365'))

# 交易配置
DEFAULT_INITIAL_CAPITAL = float(os.getenv('DEFAULT_INITIAL_CAPITAL', '10000'))
DEFAULT_COMMISSION = float(os.getenv('DEFAULT_COMMISSION', '0.001'))
DEFAULT_SLIPPAGE = float(os.getenv('DEFAULT_SLIPPAGE', '0.001'))

# API配置
API_HOST = os.getenv('API_HOST', '0.0.0.0')
API_PORT = int(os.getenv('API_PORT', '8000'))
API_RELOAD = os.getenv('API_RELOAD', 'True').lower() == 'true'

# 前端配置
FRONTEND_URL = os.getenv('FRONTEND_URL', 'http://localhost:3000')
CORS_ORIGINS = [
    FRONTEND_URL,
    "http://127.0.0.1:3000",
    "http://localhost:3000"
]

# 性能配置
MAX_WORKERS = int(os.getenv('MAX_WORKERS', '10'))
CACHE_TTL = int(os.getenv('CACHE_TTL', '3600'))  # 缓存过期时间（秒）

def setup_logging(level: str = LOG_LEVEL) -> None:
    """设置日志配置"""
    # 确保日志目录存在
    log_dir = PROJECT_ROOT / 'logs'
    log_dir.mkdir(exist_ok=True)
    
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format=LOG_FORMAT,
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(log_dir / 'system.log', mode='a')
        ]
    )

def get_config() -> Dict[str, Any]:
    """获取所有配置"""
    return {
        'project_root': PROJECT_ROOT,
        'log_level': LOG_LEVEL,
        'data_cache_size': DATA_CACHE_SIZE,
        'default_lookback_days': DEFAULT_LOOKBACK_DAYS,
        'default_initial_capital': DEFAULT_INITIAL_CAPITAL,
        'default_commission': DEFAULT_COMMISSION,
        'default_slippage': DEFAULT_SLIPPAGE,
        'api_host': API_HOST,
        'api_port': API_PORT,
        'api_reload': API_RELOAD,
        'frontend_url': FRONTEND_URL,
        'cors_origins': CORS_ORIGINS,
        'max_workers': MAX_WORKERS,
        'cache_ttl': CACHE_TTL
    }
